/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TermsConditionRouteImport } from './routes/terms-condition'
import { Route as PrivacyPolicyRouteImport } from './routes/privacy-policy'
import { Route as AboutRouteImport } from './routes/about'
import { Route as IndexRouteImport } from './routes/index'

const TermsConditionRoute = TermsConditionRouteImport.update({
  id: '/terms-condition',
  path: '/terms-condition',
  getParentRoute: () => rootRouteImport,
} as any)
const PrivacyPolicyRoute = PrivacyPolicyRouteImport.update({
  id: '/privacy-policy',
  path: '/privacy-policy',
  getParentRoute: () => rootRouteImport,
} as any)
const AboutRoute = AboutRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/privacy-policy': typeof PrivacyPolicyRoute
  '/terms-condition': typeof TermsConditionRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/privacy-policy': typeof PrivacyPolicyRoute
  '/terms-condition': typeof TermsConditionRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/privacy-policy': typeof PrivacyPolicyRoute
  '/terms-condition': typeof TermsConditionRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/about' | '/privacy-policy' | '/terms-condition'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/about' | '/privacy-policy' | '/terms-condition'
  id: '__root__' | '/' | '/about' | '/privacy-policy' | '/terms-condition'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutRoute: typeof AboutRoute
  PrivacyPolicyRoute: typeof PrivacyPolicyRoute
  TermsConditionRoute: typeof TermsConditionRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/terms-condition': {
      id: '/terms-condition'
      path: '/terms-condition'
      fullPath: '/terms-condition'
      preLoaderRoute: typeof TermsConditionRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/privacy-policy': {
      id: '/privacy-policy'
      path: '/privacy-policy'
      fullPath: '/privacy-policy'
      preLoaderRoute: typeof PrivacyPolicyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutRoute: AboutRoute,
  PrivacyPolicyRoute: PrivacyPolicyRoute,
  TermsConditionRoute: TermsConditionRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
