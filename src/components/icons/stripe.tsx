export const StripeIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 32 32"
      xmlSpace="preserve"
      {...props}
    >
      <path
        style={{
          fill: "none",
          stroke: "#000000",
          strokeWidth: "2",
          strokeMiterlimit: "10",
        }}
        d="M27,25H5c-1.105,0-2-0.895-2-2V9
	c0-1.105,0.895-2,2-2h22c1.105,0,2,0.895,2,2v14C29,24.105,28.105,25,27,25z"
      />
      <path
        id="Stripe_Logo"
        d="M24.298,15.692c0.045-0.721,0.24-1.059,0.61-1.059c0.364,0,0.552,0.344,0.578,1.059H24.298z M27,16.12
	c0-0.805-0.175-1.442-0.52-1.89c-0.351-0.455-0.877-0.688-1.546-0.688c-1.37,0-2.221,1.013-2.221,2.637
	c0,0.909,0.227,1.591,0.675,2.026c0.403,0.39,0.981,0.584,1.727,0.584c0.688,0,1.325-0.162,1.727-0.429l-0.175-1.104
	c-0.396,0.214-0.857,0.331-1.377,0.331c-0.312,0-0.526-0.065-0.682-0.201c-0.169-0.143-0.266-0.377-0.299-0.708h2.669
	C26.987,16.601,27,16.237,27,16.12z M20.558,17.211c-0.13,0.247-0.331,0.377-0.565,0.377c-0.162,0-0.305-0.032-0.435-0.097v-2.403
	c0.273-0.286,0.52-0.318,0.61-0.318c0.409,0,0.61,0.442,0.61,1.305C20.779,16.568,20.707,16.951,20.558,17.211z M21.896,14.094
	c-0.286-0.37-0.682-0.552-1.188-0.552c-0.468,0-0.877,0.195-1.26,0.604l-0.084-0.507h-1.416v6.923l1.611-0.266v-1.624
	c0.247,0.078,0.5,0.117,0.727,0.117c0.403,0,0.987-0.104,1.442-0.597c0.435-0.474,0.656-1.208,0.656-2.176
	C22.383,15.159,22.22,14.51,21.896,14.094z M15.696,13.639h1.617v5.053h-1.617V13.639z M16.508,13.139
	c0.468,0,0.844-0.383,0.844-0.851c0-0.474-0.377-0.851-0.844-0.851c-0.474,0-0.851,0.377-0.851,0.851
	C15.657,12.756,16.034,13.139,16.508,13.139z M15.001,13.57c-0.455,0-0.818,0.239-0.961,0.667l-0.097-0.597h-1.409v5.053h1.611
	v-3.28c0.201-0.247,0.487-0.336,0.877-0.336c0.084,0,0.175,0,0.286,0.019v-1.487C15.196,13.583,15.099,13.57,15.001,13.57z
	 M11.912,14.834l0.195-1.195h-1.029v-1.451l-1.383,0.228l-0.2,1.223L9.01,13.718l-0.182,1.116h0.666v2.344
	c0,0.61,0.156,1.033,0.474,1.292c0.266,0.214,0.649,0.318,1.188,0.318c0.416,0,0.669-0.071,0.844-0.117v-1.266
	c-0.097,0.026-0.318,0.071-0.468,0.071c-0.318,0-0.455-0.162-0.455-0.533v-2.111H11.912z M7.299,15.542
	c-0.474-0.175-0.734-0.312-0.734-0.526c0-0.182,0.149-0.286,0.416-0.286c0.487,0,0.987,0.188,1.331,0.357l0.195-1.201
	c-0.273-0.13-0.831-0.344-1.604-0.344c-0.546,0-1,0.143-1.325,0.409C5.24,14.23,5.065,14.633,5.065,15.12
	c0,0.883,0.539,1.26,1.416,1.578c0.565,0.201,0.753,0.344,0.753,0.565c0,0.214-0.182,0.338-0.513,0.338
	c-0.409,0-1.085-0.201-1.526-0.461L5,18.354c0.377,0.214,1.078,0.435,1.805,0.435c0.578,0,1.059-0.136,1.383-0.396
	c0.364-0.286,0.552-0.708,0.552-1.253C8.741,16.237,8.189,15.86,7.299,15.542L7.299,15.542z"
      />
    </svg>
  );
};
